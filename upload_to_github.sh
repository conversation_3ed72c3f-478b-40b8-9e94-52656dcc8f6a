#!/bin/bash

# 设置颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}"
echo "========================================"
echo "🎂 上传生日祝福网站到GitHub"
echo "========================================"
echo -e "${NC}"

# 检查Git是否安装
if ! command -v git &> /dev/null; then
    echo -e "${RED}❌ 未检测到Git，请先安装Git${NC}"
    echo -e "${YELLOW}📥 安装方法:${NC}"
    echo "  Ubuntu/Debian: sudo apt install git"
    echo "  CentOS/RHEL: sudo yum install git"
    echo "  macOS: brew install git"
    exit 1
fi

echo -e "${GREEN}✅ Git已安装${NC}"
echo

# 获取用户输入
read -p "请输入您的GitHub用户名: " GITHUB_USERNAME
read -p "请输入仓库名称 (建议: panying-birthday): " REPO_NAME

if [ -z "$REPO_NAME" ]; then
    REPO_NAME="panying-birthday"
fi

echo
echo -e "${BLUE}📋 准备上传以下文件:${NC}"
echo "  - index.html (主页面)"
echo "  - 生日快乐歌曲.mp3 (音乐文件)"
echo "  - README.md (说明文档)"
echo "  - netlify.toml (部署配置)"
echo

echo -e "${BLUE}🚀 开始上传到: https://github.com/$GITHUB_USERNAME/$REPO_NAME${NC}"
echo

# 初始化Git仓库
echo -e "${YELLOW}📦 初始化Git仓库...${NC}"
git init

# 添加所有需要的文件
echo -e "${YELLOW}📁 添加文件...${NC}"
git add index.html
git add "生日快乐歌曲.mp3"
git add README.md
git add netlify.toml
git add deploy_instructions.md

# 提交文件
echo -e "${YELLOW}💾 提交文件...${NC}"
git commit -m "🎂 Add birthday website for 潘迎 with fireworks and music"

# 设置主分支
echo -e "${YELLOW}🌿 设置主分支...${NC}"
git branch -M main

# 添加远程仓库
echo -e "${YELLOW}🔗 连接到GitHub仓库...${NC}"
git remote add origin https://github.com/$GITHUB_USERNAME/$REPO_NAME.git

# 推送到GitHub
echo -e "${YELLOW}🚀 上传到GitHub...${NC}"
git push -u origin main

if [ $? -eq 0 ]; then
    echo
    echo -e "${GREEN}========================================"
    echo "✅ 上传成功！"
    echo "========================================${NC}"
    echo
    echo -e "${BLUE}🌐 您的网站地址:${NC}"
    echo "  GitHub仓库: https://github.com/$GITHUB_USERNAME/$REPO_NAME"
    echo "  GitHub Pages: https://$GITHUB_USERNAME.github.io/$REPO_NAME"
    echo
    echo -e "${YELLOW}📝 下一步:${NC}"
    echo "  1. 访问 https://github.com/$GITHUB_USERNAME/$REPO_NAME"
    echo "  2. 进入 Settings → Pages"
    echo "  3. 选择 Source: Deploy from a branch"
    echo "  4. 选择 Branch: main"
    echo "  5. 点击 Save"
    echo "  6. 等待几分钟后访问您的网站"
    echo
else
    echo
    echo -e "${RED}❌ 上传失败，可能的原因:${NC}"
    echo "  1. 仓库不存在，请先在GitHub创建仓库"
    echo "  2. 需要输入GitHub用户名和密码/Token"
    echo "  3. 网络连接问题"
    echo
    echo -e "${YELLOW}💡 解决方案:${NC}"
    echo "  1. 访问 https://github.com/new 创建新仓库"
    echo "  2. 仓库名称使用: $REPO_NAME"
    echo "  3. 设置为Public，不要勾选任何初始化选项"
    echo "  4. 重新运行此脚本"
fi

echo
