<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音乐播放测试</title>
    <style>
        body {
            background: #000;
            color: #fff;
            font-family: Arial, sans-serif;
            padding: 20px;
            text-align: center;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
        }
        button {
            background: #ff6b9d;
            color: white;
            border: none;
            padding: 15px 30px;
            margin: 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #ff5a8a;
        }
        audio {
            width: 100%;
            margin: 10px 0;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            background: rgba(0,255,0,0.2);
            border-radius: 5px;
        }
        .error {
            background: rgba(255,0,0,0.2);
        }
    </style>
</head>
<body>
    <h1>🎵 音乐播放诊断测试</h1>
    
    <div class="test-section">
        <h2>测试1: 英文文件名音乐</h2>
        <audio id="audio1" controls>
            <source src="happy-birthday-song.mp3" type="audio/mpeg">
        </audio>
        <br>
        <button onclick="testAudio1()">测试播放</button>
        <div id="status1" class="status">等待测试...</div>
    </div>

    <div class="test-section">
        <h2>测试2: 中文文件名音乐</h2>
        <audio id="audio2" controls>
            <source src="生日快乐歌曲.mp3" type="audio/mpeg">
        </audio>
        <br>
        <button onclick="testAudio2()">测试播放</button>
        <div id="status2" class="status">等待测试...</div>
    </div>

    <div class="test-section">
        <h2>测试3: 远程音乐源</h2>
        <audio id="audio3" controls>
            <source src="https://www.learningcontainer.com/wp-content/uploads/2020/02/Kalimba.mp3" type="audio/mpeg">
        </audio>
        <br>
        <button onclick="testAudio3()">测试播放</button>
        <div id="status3" class="status">等待测试...</div>
    </div>

    <div class="test-section">
        <h2>测试4: 浏览器音频支持</h2>
        <button onclick="testBrowserSupport()">检测浏览器支持</button>
        <div id="status4" class="status">等待测试...</div>
    </div>

    <div class="test-section">
        <h2>测试5: 用户交互后播放</h2>
        <button onclick="testUserInteraction()">点击后播放音乐</button>
        <div id="status5" class="status">等待测试...</div>
    </div>

    <script>
        function updateStatus(id, message, isError = false) {
            const element = document.getElementById(id);
            element.textContent = message;
            element.className = isError ? 'status error' : 'status';
            console.log(message);
        }

        function testAudio1() {
            const audio = document.getElementById('audio1');
            updateStatus('status1', '正在测试英文文件名音乐...');
            
            audio.play().then(() => {
                updateStatus('status1', '✅ 英文文件名音乐播放成功！');
            }).catch(error => {
                updateStatus('status1', `❌ 英文文件名音乐播放失败: ${error.message}`, true);
            });
        }

        function testAudio2() {
            const audio = document.getElementById('audio2');
            updateStatus('status2', '正在测试中文文件名音乐...');
            
            audio.play().then(() => {
                updateStatus('status2', '✅ 中文文件名音乐播放成功！');
            }).catch(error => {
                updateStatus('status2', `❌ 中文文件名音乐播放失败: ${error.message}`, true);
            });
        }

        function testAudio3() {
            const audio = document.getElementById('audio3');
            updateStatus('status3', '正在测试远程音乐源...');
            
            audio.play().then(() => {
                updateStatus('status3', '✅ 远程音乐源播放成功！');
            }).catch(error => {
                updateStatus('status3', `❌ 远程音乐源播放失败: ${error.message}`, true);
            });
        }

        function testBrowserSupport() {
            updateStatus('status4', '正在检测浏览器支持...');
            
            const audio = new Audio();
            const mp3Support = audio.canPlayType('audio/mpeg');
            const wavSupport = audio.canPlayType('audio/wav');
            const oggSupport = audio.canPlayType('audio/ogg');
            
            const supportInfo = `
                MP3支持: ${mp3Support || '不支持'}
                WAV支持: ${wavSupport || '不支持'}
                OGG支持: ${oggSupport || '不支持'}
                用户代理: ${navigator.userAgent}
            `;
            
            updateStatus('status4', supportInfo);
        }

        function testUserInteraction() {
            updateStatus('status5', '正在测试用户交互播放...');
            
            // 创建音频上下文测试
            try {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();
                
                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);
                
                oscillator.frequency.setValueAtTime(440, audioContext.currentTime);
                gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 1);
                
                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + 1);
                
                updateStatus('status5', '✅ 用户交互音频播放成功！');
            } catch (error) {
                updateStatus('status5', `❌ 用户交互音频播放失败: ${error.message}`, true);
            }
        }

        // 页面加载时自动检测
        window.onload = function() {
            testBrowserSupport();
        };
    </script>
</body>
</html>
