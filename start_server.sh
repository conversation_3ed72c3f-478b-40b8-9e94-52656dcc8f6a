#!/bin/bash

# 设置颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}"
echo "========================================"
echo "🎂 生日祝福网站服务器"
echo "========================================"
echo -e "${NC}"

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo -e "${RED}❌ 未检测到Python，请先安装Python${NC}"
        echo -e "${YELLOW}📥 安装方法:${NC}"
        echo "  Ubuntu/Debian: sudo apt install python3"
        echo "  CentOS/RHEL: sudo yum install python3"
        echo "  macOS: brew install python3"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

echo -e "${GREEN}✅ Python已安装${NC}"
echo -e "${BLUE}🚀 正在启动服务器...${NC}"
echo

# 启动Python服务器
$PYTHON_CMD server.py

echo
echo -e "${YELLOW}👋 服务器已停止${NC}"
