# 🎂 祝潘迎生日快乐

一个专为潘迎制作的温馨浪漫生日祝福网站，包含动态烟花效果、生日快乐歌曲和美好祝愿。

## ✨ 功能特点

- 🎆 **动态数字烟花效果** - 绚丽的Canvas动画
- 🎵 **多源背景音乐** - 自动切换音乐源，确保播放成功
- 📱 **移动端完美适配** - 响应式设计，支持各种设备
- � **温馨祝福文案** - 特殊字体和emoji装饰
- 🌐 **远程访问支持** - 可通过局域网分享给他人
- 🎨 **黑色主题设计** - 突出烟花效果的视觉冲击

## 🚀 使用方法

### 方法一：本地访问
直接在浏览器中打开 `index.html` 文件即可。

### 方法二：远程访问（推荐）

#### Windows用户：
1. 双击运行 `start_server.bat`
2. 服务器会自动启动并打开浏览器
3. 使用显示的远程地址分享给他人

#### Linux/Mac用户：
1. 在终端中运行：`./start_server.sh`
2. 或者运行：`python3 server.py`
3. 使用显示的远程地址分享给他人

#### 手动启动：
```bash
python server.py
```

## 🎵 音乐功能

- **多音乐源支持**：自动尝试多个远程音乐源
- **智能切换**：当一个音乐源失败时自动切换到下一个
- **备用音效**：当所有音乐源都无法播放时，生成简单音效
- **用户控制**：点击右上角音乐按钮控制播放/暂停

## 🌐 远程访问说明

- 确保设备连接到同一WiFi网络
- 防火墙需要允许对应端口访问
- 移动设备可通过扫描二维码或输入IP地址访问
- 支持多人同时访问

## 💝 祝福文案

当前包含的温馨祝福：
- 💌𝙷𝚊𝚙𝚙𝚢 𝚋𝚒𝚛𝚝𝚑𝚍𝚊𝚢 🎂🥂✨ 🎊
- 🥳和你，今年 明年 年年❤️
- ✨愿无事绊心弦 所念皆如愿
- ✨愿我们一起努力 都成为彼此最好的自己

## 🛠 技术特点

- **纯前端实现**：HTML5 + CSS3 + JavaScript
- **Canvas动画**：流畅的粒子系统和烟花效果
- **Web Audio API**：音频播放和音效生成
- **响应式设计**：适配桌面和移动设备
- **跨域支持**：CORS配置，支持远程访问
- **错误处理**：完善的音乐加载失败处理机制

## 📁 文件结构

```
happy_birthday/
├── index.html          # 主页面文件
├── server.py          # Python HTTP服务器
├── start_server.bat   # Windows启动脚本
├── start_server.sh    # Linux/Mac启动脚本
└── README.md          # 说明文档
```

## 🎯 使用场景

- 生日派对现场展示
- 远程生日祝福
- 社交媒体分享
- 移动设备浏览
- 多人同时观看

## 🔧 自定义

可以轻松修改：
- 祝福文案内容
- 烟花颜色和效果
- 背景音乐源
- 动画速度和样式
