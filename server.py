#!/usr/bin/env python3
"""
简单的HTTP服务器，用于提供生日祝福网站的远程访问
支持跨域请求和移动端访问
"""

import http.server
import socketserver
import socket
import webbrowser
import os
import sys
from urllib.parse import urlparse

class CORSHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """支持CORS的HTTP请求处理器"""
    
    def end_headers(self):
        # 添加CORS头部，允许跨域访问
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
        self.send_header('Pragma', 'no-cache')
        self.send_header('Expires', '0')
        super().end_headers()
    
    def do_OPTIONS(self):
        """处理预检请求"""
        self.send_response(200)
        self.end_headers()
    
    def log_message(self, format, *args):
        """自定义日志格式"""
        print(f"[{self.log_date_time_string()}] {format % args}")

def get_local_ip():
    """获取本机IP地址"""
    try:
        # 连接到一个远程地址来获取本机IP
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except Exception:
        return "127.0.0.1"

def find_free_port(start_port=8000):
    """查找可用端口"""
    for port in range(start_port, start_port + 100):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('', port))
                return port
        except OSError:
            continue
    return None

def main():
    # 切换到脚本所在目录
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    # 查找可用端口
    port = find_free_port(8000)
    if port is None:
        print("❌ 无法找到可用端口")
        sys.exit(1)
    
    # 获取本机IP
    local_ip = get_local_ip()
    
    # 创建服务器
    try:
        with socketserver.TCPServer(("", port), CORSHTTPRequestHandler) as httpd:
            print("🎂 生日祝福网站服务器启动成功！")
            print("=" * 50)
            print(f"📱 本地访问地址: http://localhost:{port}")
            print(f"🌐 远程访问地址: http://{local_ip}:{port}")
            print("=" * 50)
            print("💡 提示:")
            print("  - 确保防火墙允许该端口访问")
            print("  - 移动设备需要连接到同一WiFi网络")
            print("  - 按 Ctrl+C 停止服务器")
            print("=" * 50)
            
            # 自动打开浏览器
            try:
                webbrowser.open(f'http://localhost:{port}')
                print("🚀 已自动打开浏览器")
            except Exception:
                print("⚠️  无法自动打开浏览器，请手动访问上述地址")
            
            print("\n🎵 服务器运行中...")
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n\n👋 服务器已停止")
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
