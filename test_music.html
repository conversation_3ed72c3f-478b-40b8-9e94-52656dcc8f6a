<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音乐测试</title>
    <style>
        body {
            background: #000;
            color: #fff;
            font-family: Arial, sans-serif;
            padding: 20px;
            text-align: center;
        }
        button {
            background: #ff6b9d;
            color: white;
            border: none;
            padding: 15px 30px;
            margin: 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #ff5a8a;
        }
        #status {
            margin: 20px 0;
            padding: 10px;
            background: rgba(255,255,255,0.1);
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>🎵 音乐功能测试</h1>
    
    <div id="status">准备测试音乐...</div>
    
    <button onclick="testMusic1()">测试音乐源1</button>
    <button onclick="testMusic2()">测试音乐源2</button>
    <button onclick="testBeep()">测试音效</button>
    <button onclick="stopAll()">停止所有</button>
    
    <audio id="audio1" controls style="display: block; margin: 20px auto;">
        <source src="https://www.learningcontainer.com/wp-content/uploads/2020/02/Kalimba.mp3" type="audio/mpeg">
    </audio>
    
    <audio id="audio2" controls style="display: block; margin: 20px auto;">
        <source src="https://file-examples.com/storage/fe68c8a7d4e2187c8d8b4e6/2017/11/file_example_MP3_700KB.mp3" type="audio/mpeg">
    </audio>

    <script>
        const status = document.getElementById('status');
        const audio1 = document.getElementById('audio1');
        const audio2 = document.getElementById('audio2');

        function updateStatus(message) {
            status.textContent = message;
            console.log(message);
        }

        function testMusic1() {
            updateStatus('测试音乐源1...');
            audio1.play().then(() => {
                updateStatus('✅ 音乐源1播放成功');
            }).catch(e => {
                updateStatus('❌ 音乐源1播放失败: ' + e.message);
            });
        }

        function testMusic2() {
            updateStatus('测试音乐源2...');
            audio2.play().then(() => {
                updateStatus('✅ 音乐源2播放成功');
            }).catch(e => {
                updateStatus('❌ 音乐源2播放失败: ' + e.message);
            });
        }

        function testBeep() {
            updateStatus('测试音效...');
            try {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();
                
                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);
                
                oscillator.frequency.setValueAtTime(523.25, audioContext.currentTime);
                gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 1);
                
                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + 1);
                
                updateStatus('✅ 音效播放成功');
            } catch (e) {
                updateStatus('❌ 音效播放失败: ' + e.message);
            }
        }

        function stopAll() {
            audio1.pause();
            audio2.pause();
            audio1.currentTime = 0;
            audio2.currentTime = 0;
            updateStatus('所有音频已停止');
        }

        // 自动测试
        setTimeout(() => {
            updateStatus('自动测试音乐源...');
            testMusic1();
        }, 2000);
    </script>
</body>
</html>
