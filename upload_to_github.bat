@echo off
chcp 65001 >nul
title 上传生日网站到GitHub

echo.
echo ========================================
echo 🎂 上传生日祝福网站到GitHub
echo ========================================
echo.

REM 检查Git是否安装
git --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未检测到Git，请先安装Git
    echo 📥 下载地址: https://git-scm.com/download/win
    pause
    exit /b 1
)

echo ✅ Git已安装
echo.

REM 提示用户输入GitHub信息
set /p GITHUB_USERNAME="请输入您的GitHub用户名: "
set /p REPO_NAME="请输入仓库名称 (建议: panying-birthday): "

if "%REPO_NAME%"=="" set REPO_NAME=panying-birthday

echo.
echo 📋 准备上传以下文件:
echo   - index.html (主页面)
echo   - 生日快乐歌曲.mp3 (音乐文件)
echo   - README.md (说明文档)
echo   - netlify.toml (部署配置)
echo.

echo 🚀 开始上传到: https://github.com/%GITHUB_USERNAME%/%REPO_NAME%
echo.

REM 初始化Git仓库
echo 📦 初始化Git仓库...
git init

REM 添加所有需要的文件
echo 📁 添加文件...
git add index.html
git add "生日快乐歌曲.mp3"
git add README.md
git add netlify.toml
git add deploy_instructions.md

REM 提交文件
echo 💾 提交文件...
git commit -m "🎂 Add birthday website for 潘迎 with fireworks and music"

REM 设置主分支
echo 🌿 设置主分支...
git branch -M main

REM 添加远程仓库
echo 🔗 连接到GitHub仓库...
git remote add origin https://github.com/%GITHUB_USERNAME%/%REPO_NAME%.git

REM 推送到GitHub
echo 🚀 上传到GitHub...
git push -u origin main

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo ✅ 上传成功！
    echo ========================================
    echo.
    echo 🌐 您的网站地址:
    echo   GitHub仓库: https://github.com/%GITHUB_USERNAME%/%REPO_NAME%
    echo   GitHub Pages: https://%GITHUB_USERNAME%.github.io/%REPO_NAME%
    echo.
    echo 📝 下一步:
    echo   1. 访问 https://github.com/%GITHUB_USERNAME%/%REPO_NAME%
    echo   2. 进入 Settings → Pages
    echo   3. 选择 Source: Deploy from a branch
    echo   4. 选择 Branch: main
    echo   5. 点击 Save
    echo   6. 等待几分钟后访问您的网站
    echo.
) else (
    echo.
    echo ❌ 上传失败，可能的原因:
    echo   1. 仓库不存在，请先在GitHub创建仓库
    echo   2. 需要输入GitHub用户名和密码/Token
    echo   3. 网络连接问题
    echo.
    echo 💡 解决方案:
    echo   1. 访问 https://github.com/new 创建新仓库
    echo   2. 仓库名称使用: %REPO_NAME%
    echo   3. 设置为Public，不要勾选任何初始化选项
    echo   4. 重新运行此脚本
)

echo.
pause
